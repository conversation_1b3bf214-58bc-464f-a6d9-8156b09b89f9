{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.11/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_database", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_database-11.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_sound", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "map_launcher", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "mapbox_maps_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.9.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/vibration-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.11/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_database", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_database-11.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_sound", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "local_auth_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "map_launcher", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "mapbox_maps_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/mapbox_maps_flutter-2.9.1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/vibration-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.8.0/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.11/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_database", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_database-11.3.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "app_links_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/", "native_build": false, "dependencies": ["gtk"], "dev_dependency": false}, {"name": "audioplayers_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "gtk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.11/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "local_auth_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "app_links_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/", "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "dependencies": [], "dev_dependency": false}, {"name": "audioplayers_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.1/", "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.11/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/", "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+15/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.2/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_database_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_database_web-0.2.6+15/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_remote_config_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.6/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_sound_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/", "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "location_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_web-6.0.1/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/pointer_interceptor_web-0.10.3/", "dependencies": [], "dev_dependency": false}, {"name": "printing", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/printing-5.14.2/", "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.4.15/", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}, {"name": "vibration_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/vibration_web-1.6.8/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_links", "dependencies": ["app_links_linux", "app_links_web"]}, {"name": "app_links_linux", "dependencies": ["gtk"]}, {"name": "app_links_web", "dependencies": []}, {"name": "audio_session", "dependencies": []}, {"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "awesome_notifications", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_database", "dependencies": ["firebase_core", "firebase_database_web"]}, {"name": "firebase_database_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_remote_config", "dependencies": ["firebase_core", "firebase_remote_config_web"]}, {"name": "firebase_remote_config_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_sound", "dependencies": ["path_provider", "flutter_sound_web"]}, {"name": "flutter_sound_web", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "gtk", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "location", "dependencies": ["location_web"]}, {"name": "location_web", "dependencies": []}, {"name": "map_launcher", "dependencies": []}, {"name": "mapbox_maps_flutter", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "pointer_interceptor", "dependencies": ["pointer_interceptor_ios", "pointer_interceptor_web"]}, {"name": "pointer_interceptor_ios", "dependencies": []}, {"name": "pointer_interceptor_web", "dependencies": []}, {"name": "printing", "dependencies": []}, {"name": "razorpay_flutter", "dependencies": ["fluttertoast"]}, {"name": "rive_common", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "vibration", "dependencies": []}, {"name": "vibration_web", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-16 15:56:32.790831", "version": "3.32.6", "swift_package_manager_enabled": {"ios": false, "macos": false}}