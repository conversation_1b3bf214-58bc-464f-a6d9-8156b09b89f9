name: connectone
description: Connect One Customer App

publish_to: 'none'

version: 1.5.5+8

environment:
  sdk: ">=2.16.1 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  amazon_cognito_identity_dart_2: ^3.6.5
  audioplayers: ^6.5.0
  auto_size_text: ^3.0.0
  awesome_dialog: ^3.0.2
  awesome_notifications: ^0.10.1
  badges: ^3.1.2
  change_app_package_name: ^1.1.0
  community_charts_flutter: ^1.0.2
  cloud_firestore: ^5.6.11
  connectivity_plus: ^6.1.4
  cupertino_icons: ^1.0.2
  dartz: ^0.10.1
  device_info_plus: ^11.5.0
  dio: ^5.3.3
  dio_smart_retry: ^7.0.1
  easy_image_viewer: ^1.2.0
  equatable: ^2.0.3
  fbroadcast: ^2.0.0
  firebase_analytics: ^11.5.2
  firebase_auth: ^5.6.2
  firebase_core: ^3.15.1
  firebase_core_platform_interface: ^6.0.0
  firebase_crashlytics: ^4.3.9
  firebase_database: ^11.3.9
  firebase_messaging: ^15.2.9
  firebase_remote_config: ^5.4.7
  flash: ^3.0.5+1
  dropdown_search: ^5.0.6
  # flutter_barcode_scanner: ^2.0.0
  flutter_bloc: ^9.1.1
  flutter_launcher_icons: ^0.14.4
  flutter_native_splash: ^2.2.19
  flutter_rating_bar: ^4.0.1
  flutter_smart_dialog: ^4.9.0+4
  fluttertoast: ^8.2.6
  get: ^4.6.1
  get_storage: ^2.0.3
  google_fonts: ^6.1.0
  http: ^1.3.0
  icons_launcher: ^3.0.1
  intl: ^0.20.0
  lazy_load_scrollview: ^1.3.0
  location: ^8.0.1
  map_launcher: ^2.4.0
  mapbox_maps_flutter: ^0.4.0
  multi_select_flutter: ^4.1.2
  #  package_info_plus: ^4.1.0
  # platform_device_id: ^1.0.1
  readmore: ^3.0.0
  rive: ^0.13.20
  rotated_corner_decoration: 2.1.0+1
  rounded_loading_button: ^2.1.0
  shared_preferences: ^2.0.17
  syncfusion_flutter_core: ^30.1.40
  syncfusion_flutter_sliders: ^30.1.40
  table_calendar: ^3.0.6
  timezone: ^0.10.1
  turf: ^0.0.7
  url_launcher: ^6.2.4
  vibration: ^3.1.3
  vibration_web: ^1.6.5
  webview_flutter: ^4.10.0
  loader_overlay: ^4.0.4
  modal_progress_hud_nsn: ^0.5.1
  flutter_screen_lock: ^9.0.4
  local_auth: ^2.1.8
  flutter_typeahead: ^5.2.0
  file_picker: ^10.2.0
  flutter_sound: ^9.2.13
  audio_session: ^0.1.21
  permission_handler: ^12.0.1
  share_plus: ^11.0.0
  razorpay_flutter: ^1.3.7
  cached_network_image: ^3.3.1
  image_picker: ^1.1.2
  path: ^1.9.0
  path_provider: ^2.1.4
  pdf: ^3.11.1
  printing: ^5.13.1
  app_links: ^6.3.3
  mime: ^2.0.0
  tutorial_coach_mark: ^1.3.0
  # social_media_recorder: ^1.1.13

  # just_audio: ^0.9.34

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

flutter_native_splash:
  color: '#ffffff'
  image: assets/images/bai.png
  color_dark: '#ffffff'
  icon_background_color_dark: '#ffffff'
  android_12:
    color: '#ffffff'
    icon_background_color: '#ffffff'
  fullscreen: true

icons_launcher:
  image_path: "assets/images/bai.png"
  platforms:
    android:
      enable: true
    ios:
      enable: true

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/sounds/
  fonts:
    - family: avenir_light
      fonts:
        - asset: assets/fonts/avenir_light.ttf
    - family: avenir_black
      fonts:
        - asset: assets/fonts/avenir_black.otf
    - family: avenir_medium
      fonts:
        - asset: assets/fonts/avenir_medium.ttf
    - family: helvetica_neu
      fonts:
        - asset: assets/fonts/helvetica_neu.otf
    - family: poppins
      fonts:
        - asset: assets/fonts/Archivo-Light.ttf
    - family: archivo
      fonts:
        - asset: assets/fonts/Archivo-Regular.ttf
